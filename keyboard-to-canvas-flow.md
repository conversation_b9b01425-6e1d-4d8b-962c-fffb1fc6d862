# 键盘输入到Canvas渲染流程分析

## 🔍 流程概览

从键盘输入到canvas渲染的完整流程如下：

```
键盘事件 → Engine监听 → InputManager分发 → Scene处理 → makeDirty标记 → 渲染循环 → Canvas绘制
```

## 📋 详细流程步骤

### 1. 🎹 键盘事件监听 (Engine)
**文件**: `packages/engine-render/src/engine.ts`
**方法**: `_handleKeyboardAction()`

- 监听canvas元素的 `keydown` 和 `keyup` 事件
- 将原生KeyboardEvent转换为IKeyboardEvent
- 通过 `onInputChanged$` 事件流发送到InputManager

**日志输出**:
```
🎹 [Engine] Keyboard Down/Up Event: { key, keyCode, type, timestamp }
🎹 [Engine] Keyboard event emitted to InputManager
```

### 2. ⌨️ 输入管理器处理 (InputManager)
**文件**: `packages/engine-render/src/scene.input-manager.ts`
**方法**: `_onInput$` 订阅处理

- 接收Engine发送的键盘事件
- 根据事件类型分发到对应的处理方法
- 调用 `_onKeyDown()` 或 `_onKeyUp()`

**日志输出**:
```
⌨️ [InputManager] Received keyboard event: { type, keyCode, timestamp }
⌨️ [InputManager] Processing keydown/keyup: { keyCode, sceneId }
⌨️ [InputManager] Keydown/Keyup event emitted to scene
```

### 3. 🎨 场景事件处理 (Scene)
**文件**: `packages/engine-render/src/scene.ts`
**事件流**: `onKeyDown$` / `onKeyUp$`

- Scene接收键盘事件并触发相应的事件流
- 其他组件可以订阅这些事件流来响应键盘输入
- 通常会触发某些对象的状态变化

### 4. 🔄 脏状态标记 (makeDirty)
**文件**: 
- `packages/engine-render/src/base-object.ts` - BaseObject.makeDirty()
- `packages/engine-render/src/scene.ts` - Scene.makeDirty()

当键盘输入导致内容变化时，相关对象会被标记为"脏"状态：
- BaseObject调用 `makeDirty(true)` 
- 传播到Layer层级
- 最终传播到Scene层级

**日志输出**:
```
🔄 [BaseObject] Making object dirty: { objectType, objectKey, layerExists }
🎨 [Scene] Making scene dirty: { sceneKey, layersCount, timestamp }
```

### 5. 🎬 渲染循环 (RenderLoop)
**文件**: `packages/engine-render/src/engine.ts`
**方法**: `_renderFunction()`

- 使用requestAnimationFrame持续运行
- 检查是否有需要渲染的任务
- 执行所有注册的渲染函数

**日志输出**:
```
🎬 [Engine] Starting render frame: { frameId, timestamp, renderTasksCount }
🎬 [Engine] Render frame completed
🎬 [Engine] Render loop stopped - no more tasks
```

### 6. 🖼️ 场景渲染 (Scene.render)
**文件**: `packages/engine-render/src/scene.ts`
**方法**: `render()`

- 检查场景是否为脏状态
- 清空canvas
- 按层级顺序渲染所有Layer

**日志输出**:
```
🖼️ [Scene] Starting scene render: { sceneKey, layersCount, isDirty, timestamp }
🖼️ [Scene] Rendering layer X/Y: { layerIndex, isMaxLayer }
🖼️ [Scene] Scene render completed
```

### 7. 🎨 图层渲染 (Layer.render)
**文件**: `packages/engine-render/src/layer.ts`
**方法**: `render()`

- 渲染该层的所有对象
- 支持缓存机制优化性能
- 调用每个对象的render方法

**日志输出**:
```
🎨 [Layer] Rendering layer: { layerIndex, isDirty, allowCache, isMaxLayer, objectsCount }
```

## 📊 特殊场景：表格编辑

### 表格单元格编辑器
**文件**: `packages/sheets-ui/src/controllers/render-controllers/editor-bridge.render-controller.ts`

当键盘输入触发单元格编辑时：

**日志输出**:
```
📝 [EditorBridge] Showing editor by keyboard: { eventData, inputType, keycode, unitId }
```

### 表格渲染控制器
**文件**: `packages/sheets-ui/src/controllers/render-controllers/sheet.render-controller.ts`

**日志输出**:
```
📊 [SheetRender] Context activated - starting render loop
📊 [SheetRender] Executing frame function - calling scene.render()
📊 [SheetRender] Context deactivated - stopping render loop
```

## 🔧 调试建议

1. **打开浏览器控制台**，输入任意键盘按键
2. **观察日志顺序**，确认事件流是否正常
3. **检查性能**，注意渲染频率是否合理
4. **验证脏状态**，确认只有必要的对象被重新渲染

## 🚨 注意事项

1. **性能优化**: 避免频繁的makeDirty调用
2. **事件冒泡**: 确保事件正确传播到目标对象
3. **内存泄漏**: 及时清理事件监听器
4. **渲染批处理**: 合并多个变更到单次渲染

## 📈 性能监控

系统内置了性能监控，可以通过以下方式查看：
- FPS监控
- 帧时间统计
- 渲染任务计数

通过这些日志，您可以清楚地追踪从键盘输入到最终canvas渲染的完整流程。
